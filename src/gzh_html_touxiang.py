import os
import time
import logging
import datetime
from logging.handlers import TimedRotatingFileHandler
from dotenv import load_dotenv
from wechatpy import WeChatClient
import coloredlogs
import os
import sys
from pathlib import Path
project_root = Path(__file__).resolve().parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))


from src.llm.llm_groq import call_content_common
# from src.llm.llm_poe import call_content_common
from src.utils import (
    re_util,
    notion_util,
    gzh_parse_util,
    markdown_html_util,
    downimg_util,
    gzh_api,
    watermark_util
)

# Configure logging
log_dir = "logs"
os.makedirs(log_dir, exist_ok=True)

log = logging.getLogger('gzh_touxiang')
log.setLevel(logging.DEBUG)

file_handler = TimedRotatingFileHandler(
    filename=os.path.join(log_dir, 'gzh_touxiang.log'),
    when='midnight',
    interval=1,
    backupCount=30,
    encoding='utf-8'
)
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(
    logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
)
log.addHandler(file_handler)

coloredlogs.install(
    level='INFO',
    logger=log,
    fmt='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%H:%M:%S',
    level_styles={
        'debug': {'color': 'white'},
        'info': {'color': 'green'},
        'warning': {'color': 'yellow'},
        'error': {'color': 'red', 'bold': True},
        'critical': {'color': 'red', 'bold': True, 'background': 'white'}
    }
)

# Load environment variables
load_dotenv()


# Global configurations
CONFIGS = {
    'area': 'bizhitouxiang',
    'author': ['咸鱼猪图集','阿甘号船长','偶然剧场', '悦语清言','米酒说球球','球场放大镜','CuMamba'],
    'readcount': 1000,
    'days': 15,
    'max_articles': 2,
    'destination_folder': os.environ.get("TOUXIANG_DESTINATION_FOLDER", "/tmp/文章存档/头像/待发布/"),
    'image_save_path': os.environ.get("TOUXIANG_IMAGE_SAVE_PATH", "/tmp/文章存档/Images/公众号/头像/"),
    'notion_token': os.environ.get("NOTION_TOKEN"),
    'notion_database': os.environ.get("NOTION_DATABASE_YZCM"),
    'wechat_configs': [
        {
            'name': 'jinjin_touxiang',
            'app_id': os.getenv('JINJIN_WECHAT_APP_ID'),
            'app_secret': os.getenv('JINJIN_WECHAT_APP_SECRET'),
            'profile': {
                'nickname': '金金头像',  # 公众号名称
                'alias': 'jinjintouxiang',  # 公众号
                'headimg': 'http://mmbiz.qpic.cn/mmbiz_png/ceU5DCPdCPqwD3Bhf2AuDUJXvtUNxXm14X9LVvrqEO8llE7z0BQRD5lcsO4xoQX4bs9Xia2U5IonlL2xgw2S6aQ/0?wx_fmt=png',  # 公众号二维码图片路径
                'signature': '金金头像，全是漂亮精美的头像！',  # 公众号签名
                'publisher': '金金',
                'id': 'Mzk0NzcwNTY1Ng==',  # 公众号ID
            }
        },
    ]
}

class ArticleProcessor:
    def __init__(self, wechat_config_name=None):
        """
        初始化文章处理器
        :param wechat_config_name: 指定要使用的微信配置名称，如果为None则使用第一个配置
        """
        self.notion = notion_util.notion_client(
            token=CONFIGS['notion_token'],
            database_id=CONFIGS['notion_database'],
            logger=log
        )
        self.clients = {}
        self._initialize_wechat_clients(wechat_config_name)

    def _initialize_wechat_clients(self, wechat_config_name=None):
        """
        初始化微信客户端
        :param wechat_config_name: 指定要初始化的微信配置名称，如果为None则初始化所有配置
        """
        for config in CONFIGS['wechat_configs']:
            if wechat_config_name is None or config['name'] == wechat_config_name:
                self.clients[config['name']] = gzh_api.WECHATCLIENT(
                    config['app_id'],
                    config['app_secret']
                )


    def get_client(self, name=None):
        """
        获取指定名称的微信客户端
        :param name: 客户端名称，如为None则返回第一个客户端
        :return: 微信客户端实例
        """
        if not self.clients:
            raise ValueError("No WeChat clients initialized")

        if name is None:
            return next(iter(self.clients.values()))

        if name not in self.clients:
            raise ValueError(f"WeChat client '{name}' not found")

        return self.clients[name]

    def get_two_months_ago(self):
        """获取当前日期两个月以前的日期"""
        current_date = datetime.datetime.now()
        two_months_ago = current_date - datetime.timedelta(days=60)
        return two_months_ago.strftime('%Y-%m-%d')


    def get_article_data(self):
        """从Notion获取数据-公众号文章URL"""
        try:
            for author in CONFIGS['author']:
                params = {
                    'readcount': CONFIGS['readcount'],
                    # 'author': CONFIGS['author'],
                    'author': author,
                    'datasource': '公众号',
                    # 'area': 'bizhitouxiang',
                    # 'yesterday': self.get_two_months_ago()
                }
                content_dict = self.notion.get_content_by_condition_coommon(params=params)
                if not content_dict:
                    log.info(f'No matching articles found for author: {author}')
                    return None, None

                page_id = content_dict['page_id']
                page_url = content_dict['page_url']
                page_title = content_dict['page_title'].replace('/', '-')

                log.info(f'Processing: title={page_title}, id={page_id}, url={page_url}')
                self.notion.update_page_properties(
                    page_id=page_id,
                    tags='格式化',
                    area=None
                )
                return page_url, page_id
        except Exception as e:
            log.error(f"Error in get_article_data: {str(e)}", exc_info=True)
        return None, None

    def process_images(self, pic_list, weixin_title):
        """处理文章图片"""
        save_path = os.path.join(CONFIGS['image_save_path'], weixin_title)
        os.makedirs(save_path, exist_ok=True)


        # 过滤图片列表
        valid_formats = ['mmbiz_jpg', 'mmbiz_png', 'mmbiz_webp']
        pic_list = [url for url in pic_list
                   if not url.startswith('http://')  # 排除http链接
                   and not any(gif in url for gif in ['wx_fmt=gif', 'mmbiz_gif'])  # 排除gif
                   and any(fmt in url for fmt in valid_formats)]  # 包含合法格式


        # 过滤掉大小小于10KB或大于2MB的图片
        new_pic_list = []
        for img_url in pic_list:
            image_down_path = downimg_util.down_image_from_url(
                url=img_url,
                save_path=save_path
            )
            file_size = os.path.getsize(image_down_path)
            if file_size < 10 * 1024:  # 小于10KB
                continue
            else:
                new_pic_list.append(image_down_path)


        new_image_list = []
        thumb_media_id = None
        for idx, image_down_path in enumerate(new_pic_list):
            try:
                # 水印处理
                watermark_util.handle_image(image_down_path)

                # 上传云端
                gzh_img_url = self.get_client().upload_article_image(image_down_path)
                if gzh_img_url:
                    new_image_list.append(gzh_img_url)

                log.info(f"Uploading image list: {[image_down_path]}")
                if idx == len(pic_list) - 2:
                    thumb_media_id = self.get_client().upload_cover_image(image_down_path)
                    # thumb_media_id = self.client.upload_cover_image(image_down_path)
            except Exception as e:
                log.error(f"处理图片时出错 {img_url}: {e}", exc_info=True)
                continue

        return new_image_list, thumb_media_id

    def process_articles(self):
        """处理文章主流程"""
        articles = []
        page_ids = []
        count = 0

        try:
            # Get the profile for the current WeChat account
            current_account = next(iter(self.clients.keys()))  # Get the current account name
            profile = self.get_account_profile(current_account)

            while count < 10:
                count += 1
                docx_files = [f for f in os.listdir(CONFIGS['destination_folder'])
                            if f.endswith('.html')]

                if len(docx_files) >= CONFIGS['max_articles']:
                    log.info(f"HTML files count: {len(docx_files)}, exceeding limit")
                    break

                # Get and process article
                page_url, page_id = self.get_article_data()
                # page_url='https://mp.weixin.qq.com/s?__biz=MzkxMzQyMjE3MQ==&mid=**********&idx=6&sn=4a19495804ef154ca98c5129bc41634b&chksm=c0309324d5fa18863a3e9b0a8af38b059b0bb552fa5570b8e5c1cf57fb90249495e5c8c4aeb2&scene=0&xtrack=1#rd'
                # page_id='15928842492281008bfeec9374302b39'
                if not page_url:
                    continue

                weixin_title, text_content, pic_list = gzh_parse_util.get_weixin_content(page_url)
                if not weixin_title or len(pic_list)==0:
                    self.notion.update_page_properties(page_id=page_id, tags='内容失效')
                    continue

                # llm
                new_content = call_content_common(weixin_title,page_url,CONFIGS['area'])
                if not new_content:
                    self.notion.update_page_properties(page_id=page_id, tags='生成失败')
                    continue
                try:
                    title, content = re_util.handle_content(new_content)
                except Exception as e:
                    log.error(f"处理文章内容时出错 {page_id}: {e}", exc_info=True)
                    title = weixin_title
                    content = new_content

                # Process images
                new_image_list, thumb_media_id = self.process_images(pic_list, weixin_title)
                # new_image_list=['http://mmbiz.qpic.cn/sz_mmbiz_png/wQf2dOmqSZwyXJAuINPwMBHpY5oDDWf0oMlcO1icpbSjhN5HtN422uG8hDJ6kRMflIvZia4CmtyfBfW2JfejYEow/0?from=appmsg', 'http://mmbiz.qpic.cn/sz_mmbiz_png/wQf2dOmqSZwyXJAuINPwMBHpY5oDDWf0hzcG0blwRoFglFr2jurCrqb440qGNEgOibKtXVQ0ewzRkjPOeA4kNGg/0?from=appmsg', 'http://mmbiz.qpic.cn/sz_mmbiz_png/wQf2dOmqSZwyXJAuINPwMBHpY5oDDWf0WlnEMvqInejnCN0Hibo3LOLI7J6VtBW4tEe3jyKEibia86FXPeJj0Fwcg/0?from=appmsg', 'http://mmbiz.qpic.cn/sz_mmbiz_png/wQf2dOmqSZwyXJAuINPwMBHpY5oDDWf0bASkwtUXe4lKA4OE85YyibKSXp5ccnokxmx2hibibLLev1mxpRNK8xrgg/0?from=appmsg', 'http://mmbiz.qpic.cn/sz_mmbiz_png/wQf2dOmqSZwyXJAuINPwMBHpY5oDDWf01VdCJ1Xw7vK3e7ISuh2DQyzvh1RmHW0hdicJfQkTKsBnAYNBpMECic3Q/0?from=appmsg', 'http://mmbiz.qpic.cn/sz_mmbiz_png/wQf2dOmqSZwyXJAuINPwMBHpY5oDDWf0eA2HQnhsibjiamibIlNQGePYpiaDS43giaYtnFoFgYAcOuKB8tpqI7mkXZw/0?from=appmsg', 'http://mmbiz.qpic.cn/sz_mmbiz_png/wQf2dOmqSZwyXJAuINPwMBHpY5oDDWf0SzVuhnSfCm4rRjVmnpRcgOx22cSEFnh2YnHOtUaL7kmRsbdkNiag0rg/0?from=appmsg', 'http://mmbiz.qpic.cn/sz_mmbiz_png/wQf2dOmqSZwyXJAuINPwMBHpY5oDDWf0KhETSshDu9FUYqqPEFZzZGvicJnQGAQt7roAp8YXZdLpjR1PAEh5O3g/0?from=appmsg']
                if not thumb_media_id:
                    log.error('No cover image ID')
                    self.notion.update_page_content(page_id=page_id, properties_params="初始化")
                    continue

                if not new_image_list:
                    raise ValueError("没有成功上传任何图片")

                # 获取历史文章:
                history_articles = self.get_client().get_published_list_api()

                # Generate HTML
                file_path = os.path.join(CONFIGS['destination_folder'], f'{weixin_title}.html')
                # result = markdown_html_util.markdown_to_html_with_images(
                #     "", file_path, new_image_list, history_articles,profile,logger=log
                # )
                result = markdown_html_util.markdown_to_html_with_images_content(
                    content, file_path, new_image_list, history_articles,profile,logger=log
                )
                if not result:
                    self.notion.update_page_properties(page_id=page_id, tags='初始化')
                    continue

                with open(file_path, 'r', encoding='utf-8') as file:
                        html_content = file.read()
                        log.debug("Disclaimer class present in output: " +
                                str('class="disclaimer"' in html_content))

                if not thumb_media_id:
                    self.notion.update_page_content(page_id=page_id, properties_params="初始化")
                    continue

                article = {
                    # 'title': weixin_title,
                    'title': title,
                    'author': profile.get('publisher', '金金'),
                    'content': html_content,
                    'thumb_media_id': thumb_media_id,
                    'digest': text_content[:80]
                }

                articles.append(article)
                page_ids.append(page_id)
                time.sleep(1)

            # Create draft
            if articles:
                media_id = self.get_client().create_draft(articles)
                log.info(f"Successfully created draft with media_id: {media_id}")
                return media_id, page_ids

        except Exception as e:
            log.error(f"Error in article processing: {e}", exc_info=True)
            return None, page_ids

    def get_account_profile(self, account_name):
        """Get profile information for a specific WeChat account"""
        for config in CONFIGS['wechat_configs']:
            if config['name'] == account_name:
                return config.get('profile', {})
        return None

def cleanup_html_files():
    """清理HTML文件"""
    try:
        for f in os.listdir(CONFIGS['destination_folder']):
            if f.endswith('.html'):
                os.remove(os.path.join(CONFIGS['destination_folder'], f))
        log.info("Cleanup complete")
    except Exception as e:
        log.error(f"Cleanup failed: {e}", exc_info=True)


def verify_wechat_token():
    """验证微信token"""
    verification_results = {}

    for config in CONFIGS['wechat_configs']:
        try:
            client = WeChatClient(config['app_id'], config['app_secret'])
            token = client.access_token
            verification_results[config['name']] = True
            log.info(f'Successfully verified WeChat token for account: {config["name"]}')
        except Exception as e:
            verification_results[config['name']] = False
            log.error(f'Failed to verify WeChat token for account {config["name"]}: {e}')

    return verification_results

def main():
    """主函数"""
    os.makedirs(CONFIGS['TOUXIANG_DESTINATION_FOLDER'], exist_ok=True)
    os.makedirs(CONFIGS['TOUXIANG_IMAGE_SAVE_PATH'], exist_ok=True)

    # Verify tokens for all WeChat configurations
    verification_results = verify_wechat_token()
    valid_configs = [name for name, is_valid in verification_results.items() if is_valid]

    if not valid_configs:
        log.error("No valid WeChat tokens found. Exiting...")
        return


    results = []
    for wechat_config in CONFIGS['wechat_configs']:
        if wechat_config['name'] not in valid_configs:
            continue

        try:
            log.info(f"Processing articles for WeChat account: {wechat_config['name']}")
            processor = ArticleProcessor(wechat_config_name=wechat_config['name'])

            cleanup_html_files()
            media_id, page_ids = processor.process_articles()

            results.append({
                'wechat_name': wechat_config['name'],
                'media_id': media_id,
                'page_ids': page_ids
            })

            log.info(f"Successfully processed articles for {wechat_config['name']}")
        except Exception as e:
            log.error(f"Error processing articles for {wechat_config['name']}: {e}", exc_info=True)
            continue

    # Summary of processing results
    log.info("Processing summary:")
    for result in results:
        log.info(f"WeChat account {result['wechat_name']}: Media ID {result['media_id']}")

    return results


if __name__ == "__main__":
    main()
    log.info('程序结束...')
